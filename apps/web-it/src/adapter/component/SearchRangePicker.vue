<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { computed } from 'vue';

import { RangePicker } from 'ant-design-vue';

interface Props {
  value?: [Dayjs, Dayjs] | null;
  modelValue?: [Dayjs, Dayjs] | null;
}

interface Emits {
  (e: 'update:value', value: [Dayjs, Dayjs] | null): void;
  (e: 'update:modelValue', value: [Dayjs, Dayjs] | null): void;
  (e: 'change', value: [Dayjs, Dayjs] | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 处理日期范围变化，自动设置结束时间为 23:59:59
const handleChange = (dates: [Dayjs, Dayjs] | null) => {
  let processedDates = dates;

  if (dates && dates.length === 2 && dates[0] && dates[1]) {
    const [startDate, endDate] = dates;
    // 将结束时间设置为当天的 23:59:59
    const adjustedStartDate = startDate.hour(0).minute(0).second(0).millisecond(0);
    const adjustedEndDate = endDate.hour(23).minute(59).second(59).millisecond(999);
    processedDates = [adjustedStartDate, adjustedEndDate];
  }

  // 发出所有相关事件
  emit('update:value', processedDates);
  emit('update:modelValue', processedDates);
  emit('change', processedDates);
};

// 计算当前值，优先使用 modelValue，然后是 value
const currentValue = computed(() => {
  return props.modelValue || props.value;
});
</script>
<template>
  <RangePicker :value="currentValue" v-bind="$attrs" @change="handleChange" />
</template>
